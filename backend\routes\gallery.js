import express from 'express';
import { gallery } from '../models/data.js';
import { authenticateToken } from './auth.js';

const router = express.Router();

// Get all gallery items (public)
router.get('/', (req, res) => {
  try {
    const { category } = req.query;
    
    let filteredGallery = gallery;
    if (category) {
      filteredGallery = gallery.filter(item => 
        item.category.toLowerCase() === category.toLowerCase()
      );
    }

    const sortedGallery = filteredGallery.sort((a, b) => 
      new Date(b.createdAt) - new Date(a.createdAt)
    );

    res.json({
      success: true,
      data: sortedGallery,
      total: sortedGallery.length
    });
  } catch (error) {
    console.error('Get gallery error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get single gallery item by ID (public)
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const galleryItem = gallery.find(item => item.id === parseInt(id));

    if (!galleryItem) {
      return res.status(404).json({
        error: 'Gallery item not found'
      });
    }

    res.json({
      success: true,
      data: galleryItem
    });
  } catch (error) {
    console.error('Get gallery by ID error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Create new gallery item (admin only)
router.post('/', authenticateToken, (req, res) => {
  try {
    const { title, description, imageUrl, category } = req.body;

    if (!title || !imageUrl) {
      return res.status(400).json({
        error: 'Title and image URL are required'
      });
    }

    const newGalleryItem = {
      id: Math.max(...gallery.map(g => g.id), 0) + 1,
      title,
      description: description || '',
      imageUrl,
      category: category || 'Umum',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    gallery.push(newGalleryItem);

    res.status(201).json({
      success: true,
      message: 'Gallery item created successfully',
      data: newGalleryItem
    });
  } catch (error) {
    console.error('Create gallery error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update gallery item (admin only)
router.put('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { title, description, imageUrl, category } = req.body;

    const galleryIndex = gallery.findIndex(item => item.id === parseInt(id));
    if (galleryIndex === -1) {
      return res.status(404).json({
        error: 'Gallery item not found'
      });
    }

    // Update gallery item
    const updatedGallery = {
      ...gallery[galleryIndex],
      ...(title && { title }),
      ...(description !== undefined && { description }),
      ...(imageUrl && { imageUrl }),
      ...(category && { category }),
      updatedAt: new Date().toISOString()
    };

    gallery[galleryIndex] = updatedGallery;

    res.json({
      success: true,
      message: 'Gallery item updated successfully',
      data: updatedGallery
    });
  } catch (error) {
    console.error('Update gallery error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Delete gallery item (admin only)
router.delete('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const galleryIndex = gallery.findIndex(item => item.id === parseInt(id));

    if (galleryIndex === -1) {
      return res.status(404).json({
        error: 'Gallery item not found'
      });
    }

    gallery.splice(galleryIndex, 1);

    res.json({
      success: true,
      message: 'Gallery item deleted successfully'
    });
  } catch (error) {
    console.error('Delete gallery error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
