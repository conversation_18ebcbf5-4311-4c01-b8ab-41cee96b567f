import dotenv from 'dotenv';
import connectDB from '../config/database.js';
import seedDatabase from '../utils/seeder.js';

// Load environment variables
dotenv.config();

const runSeeder = async () => {
  try {
    // Connect to database
    await connectDB();
    
    // Run seeder
    await seedDatabase();
    
    console.log('✅ Seeding completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('❌ Seeding failed:', error);
    process.exit(1);
  }
};

runSeeder();
