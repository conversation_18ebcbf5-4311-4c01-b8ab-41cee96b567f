import mongoose from 'mongoose';

const settingsSchema = new mongoose.Schema({
  key: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  value: {
    type: mongoose.Schema.Types.Mixed,
    required: true
  },
  type: {
    type: String,
    enum: ['string', 'number', 'boolean', 'object', 'array'],
    default: 'string'
  },
  description: {
    type: String,
    trim: true
  },
  category: {
    type: String,
    enum: ['general', 'contact', 'social', 'appearance', 'system'],
    default: 'general'
  },
  isPublic: {
    type: Boolean,
    default: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true
});

// Index for efficient queries
settingsSchema.index({ key: 1 });
settingsSchema.index({ category: 1, isPublic: 1 });

// Static method to get setting by key
settingsSchema.statics.getSetting = async function(key, defaultValue = null) {
  try {
    const setting = await this.findOne({ key });
    return setting ? setting.value : defaultValue;
  } catch (error) {
    return defaultValue;
  }
};

// Static method to set setting
settingsSchema.statics.setSetting = async function(key, value, updatedBy, options = {}) {
  const { type = 'string', description = '', category = 'general', isPublic = true } = options;
  
  return await this.findOneAndUpdate(
    { key },
    { 
      value, 
      type, 
      description, 
      category, 
      isPublic, 
      updatedBy 
    },
    { 
      upsert: true, 
      new: true, 
      runValidators: true 
    }
  );
};

// Static method to get all public settings
settingsSchema.statics.getPublicSettings = async function() {
  try {
    const settings = await this.find({ isPublic: true }).select('key value type');
    const result = {};
    
    settings.forEach(setting => {
      result[setting.key] = setting.value;
    });
    
    return result;
  } catch (error) {
    return {};
  }
};

const Settings = mongoose.model('Settings', settingsSchema);

export default Settings;
