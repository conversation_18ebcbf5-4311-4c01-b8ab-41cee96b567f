import React from 'react';

const About = () => {
  return (
    <div className="max-w-4xl mx-auto">
      {/* Header Section */}
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-4">
          Tentang <PERSON>
        </h1>
        <p className="text-xl text-gray-600">
          Membangun masa depan pendidikan dengan teknologi modern
        </p>
      </div>

      {/* Mission & Vision */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
        <div className="bg-white p-8 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Visi Kami</h2>
          <p className="text-gray-600 leading-relaxed">
            Menjadi platform terdepan dalam sistem manajemen sekolah yang mengintegrasikan
            teknologi modern untuk menciptakan lingkungan pendidikan yang efisien,
            transparan, dan berkelanjutan.
          </p>
        </div>

        <div className="bg-white p-8 rounded-lg shadow-md">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Misi Kami</h2>
          <ul className="text-gray-600 space-y-2">
            <li>• Menyediakan solusi teknologi yang mudah digunakan</li>
            <li>• Meningkatkan efisiensi administrasi sekolah</li>
            <li>• Mendukung transparansi dalam pengelolaan data</li>
            <li>• Memfasilitasi komunikasi yang lebih baik</li>
          </ul>
        </div>
      </div>

      {/* Team Section */}
      <div className="bg-white rounded-lg shadow-md p-8 mb-12">
        <h2 className="text-3xl font-bold text-center text-gray-900 mb-8">Tim Kami</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-24 h-24 bg-blue-100 rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg className="w-12 h-12 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">John Doe</h3>
            <p className="text-gray-600 mb-2">Lead Developer</p>
            <p className="text-sm text-gray-500">Spesialis dalam pengembangan full-stack dan arsitektur sistem</p>
          </div>

          <div className="text-center">
            <div className="w-24 h-24 bg-green-100 rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg className="w-12 h-12 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Jane Smith</h3>
            <p className="text-gray-600 mb-2">UI/UX Designer</p>
            <p className="text-sm text-gray-500">Ahli dalam desain antarmuka yang user-friendly dan responsif</p>
          </div>

          <div className="text-center">
            <div className="w-24 h-24 bg-purple-100 rounded-full mx-auto mb-4 flex items-center justify-center">
              <svg className="w-12 h-12 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Mike Johnson</h3>
            <p className="text-gray-600 mb-2">Product Manager</p>
            <p className="text-sm text-gray-500">Berpengalaman dalam manajemen produk dan strategi bisnis</p>
          </div>
        </div>
      </div>

      {/* Contact CTA */}
      <div className="bg-blue-600 text-white rounded-lg p-8 text-center">
        <h2 className="text-3xl font-bold mb-4">Siap Memulai?</h2>
        <p className="text-xl mb-6">
          Hubungi kami untuk konsultasi gratis tentang kebutuhan sistem manajemen sekolah Anda
        </p>
        <button className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition duration-300">
          Hubungi Kami
        </button>
      </div>
    </div>
  );
};

export default About;
