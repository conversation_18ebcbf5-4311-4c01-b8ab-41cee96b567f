// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`classValidatorResolver > should return a single error from classValidatorResolver when validation fails 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "birthYear must not be greater than 2013",
      "ref": undefined,
      "type": "max",
    },
    "email": {
      "message": "email must be an email",
      "ref": {
        "name": "email",
      },
      "type": "isEmail",
    },
    "like": [
      {
        "name": {
          "message": "name must be longer than or equal to 4 characters",
          "ref": undefined,
          "type": "isLength",
        },
      },
    ],
    "password": {
      "message": "password must match /^[a-zA-Z0-9]{3,30}/ regular expression",
      "ref": {
        "name": "password",
      },
      "type": "matches",
    },
    "username": {
      "message": "username must be longer than or equal to 3 characters",
      "ref": {
        "name": "username",
      },
      "type": "isLength",
    },
  },
  "values": {},
}
`;

exports[`classValidatorResolver > should return a single error from classValidatorResolver with \`mode: sync\` when validation fails 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "birthYear must not be greater than 2013",
      "ref": undefined,
      "type": "max",
    },
    "email": {
      "message": "email must be an email",
      "ref": {
        "name": "email",
      },
      "type": "isEmail",
    },
    "like": [
      {
        "name": {
          "message": "name must be longer than or equal to 4 characters",
          "ref": undefined,
          "type": "isLength",
        },
      },
    ],
    "password": {
      "message": "password must match /^[a-zA-Z0-9]{3,30}/ regular expression",
      "ref": {
        "name": "password",
      },
      "type": "matches",
    },
    "username": {
      "message": "username must be longer than or equal to 3 characters",
      "ref": {
        "name": "username",
      },
      "type": "isLength",
    },
  },
  "values": {},
}
`;

exports[`classValidatorResolver > should return all the errors from classValidatorResolver when validation fails with \`validateAllFieldCriteria\` set to true 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "birthYear must not be greater than 2013",
      "ref": undefined,
      "type": "max",
      "types": {
        "max": "birthYear must not be greater than 2013",
        "min": "birthYear must not be less than 1900",
      },
    },
    "email": {
      "message": "email must be an email",
      "ref": {
        "name": "email",
      },
      "type": "isEmail",
      "types": {
        "isEmail": "email must be an email",
      },
    },
    "like": [
      {
        "name": {
          "message": "name must be longer than or equal to 4 characters",
          "ref": undefined,
          "type": "isLength",
          "types": {
            "isLength": "name must be longer than or equal to 4 characters",
          },
        },
      },
    ],
    "password": {
      "message": "password must match /^[a-zA-Z0-9]{3,30}/ regular expression",
      "ref": {
        "name": "password",
      },
      "type": "matches",
      "types": {
        "matches": "password must match /^[a-zA-Z0-9]{3,30}/ regular expression",
      },
    },
    "username": {
      "message": "username must be longer than or equal to 3 characters",
      "ref": {
        "name": "username",
      },
      "type": "isLength",
      "types": {
        "isLength": "username must be longer than or equal to 3 characters",
        "matches": "username must match /^\\w+$/ regular expression",
      },
    },
  },
  "values": {},
}
`;

exports[`classValidatorResolver > should return all the errors from classValidatorResolver when validation fails with \`validateAllFieldCriteria\` set to true and \`mode: sync\` 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "birthYear must not be greater than 2013",
      "ref": undefined,
      "type": "max",
      "types": {
        "max": "birthYear must not be greater than 2013",
        "min": "birthYear must not be less than 1900",
      },
    },
    "email": {
      "message": "email must be an email",
      "ref": {
        "name": "email",
      },
      "type": "isEmail",
      "types": {
        "isEmail": "email must be an email",
      },
    },
    "like": [
      {
        "name": {
          "message": "name must be longer than or equal to 4 characters",
          "ref": undefined,
          "type": "isLength",
          "types": {
            "isLength": "name must be longer than or equal to 4 characters",
          },
        },
      },
    ],
    "password": {
      "message": "password must match /^[a-zA-Z0-9]{3,30}/ regular expression",
      "ref": {
        "name": "password",
      },
      "type": "matches",
      "types": {
        "matches": "password must match /^[a-zA-Z0-9]{3,30}/ regular expression",
      },
    },
    "username": {
      "message": "username must be longer than or equal to 3 characters",
      "ref": {
        "name": "username",
      },
      "type": "isLength",
      "types": {
        "isLength": "username must be longer than or equal to 3 characters",
        "matches": "username must match /^\\w+$/ regular expression",
      },
    },
  },
  "values": {},
}
`;

exports[`validate data with transformer option 1`] = `
{
  "errors": {
    "random": {
      "message": "All fields must be defined.",
      "ref": undefined,
      "type": "isDefined",
      "types": {
        "isDefined": "All fields must be defined.",
        "isNumber": "Must be a number",
        "max": "Cannot be greater than 255",
        "min": "Cannot be lower than 0",
      },
    },
  },
  "values": {},
}
`;

exports[`validate data with validator option 1`] = `
{
  "errors": {
    "random": {
      "message": "All fields must be defined.",
      "ref": undefined,
      "type": "isDefined",
      "types": {
        "isDefined": "All fields must be defined.",
      },
    },
  },
  "values": {},
}
`;
