import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import Sidebar from './Sidebar';

const AdminLayout = ({ children }) => {
  const navigate = useNavigate();

  useEffect(() => {
    // Check authentication
    const authData = localStorage.getItem('adminAuth');
    if (!authData) {
      navigate('/admin/login');
      return;
    }

    try {
      const parsedAuth = JSON.parse(authData);
      if (!parsedAuth.isAuthenticated) {
        navigate('/admin/login');
      }
    } catch (error) {
      localStorage.removeItem('adminAuth');
      navigate('/admin/login');
    }
  }, [navigate]);

  return (
    <div className="flex min-h-screen bg-gray-100">
      <Sidebar />
      <main className="flex-1 overflow-auto">
        {children}
      </main>
    </div>
  );
};

export default AdminLayout;
