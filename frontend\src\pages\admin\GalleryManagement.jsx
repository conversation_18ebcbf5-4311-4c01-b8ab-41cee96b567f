import React, { useState } from 'react';
import AdminLayout from '../../components/admin/AdminLayout';
import Modal from '../../components/admin/Modal';

const GalleryManagement = () => {
  const [images, setImages] = useState([
    {
      id: 1,
      title: "Upacara Bendera Hari Senin",
      description: "Kegiatan rutin upacara bendera setiap hari <PERSON> di halaman sekolah",
      url: "https://via.placeholder.com/800x600/3B82F6/FFFFFF?text=Upacara+Bendera",
      category: "Kegiatan Rutin",
      featured: true,
      date: "2024-12-15",
      uploadedBy: "Admin Sekolah"
    },
    {
      id: 2,
      title: "Festival Seni dan Budaya 2024",
      description: "Penampilan tari tradisional dalam acara festival seni dan budaya tahunan",
      url: "https://via.placeholder.com/800x600/10B981/FFFFFF?text=Festival+Seni",
      category: "Kegiatan Khusus",
      featured: true,
      date: "2024-12-10",
      uploadedBy: "Koordinator Seni"
    },
    {
      id: 3,
      title: "Olimpiade Matematika Nasional",
      description: "Tim olimpiade matematika sekolah saat mengikuti kompetisi tingkat nasional",
      url: "https://via.placeholder.com/800x600/F59E0B/FFFFFF?text=Olimpiade+Matematika",
      category: "Prestasi",
      featured: false,
      date: "2024-12-05",
      uploadedBy: "Guru Matematika"
    },
    {
      id: 4,
      title: "Laboratorium Komputer Baru",
      description: "Fasilitas laboratorium komputer yang baru direnovasi dengan perangkat modern",
      url: "https://via.placeholder.com/600x400/8B5CF6/FFFFFF?text=Lab+Komputer",
      category: "Fasilitas",
      featured: false,
      date: "2024-11-28",
      uploadedBy: "Admin Sekolah"
    },
    {
      id: 5,
      title: "Kegiatan Ekstrakurikuler Basket",
      description: "Latihan rutin tim basket sekolah di lapangan olahraga",
      url: "https://via.placeholder.com/600x400/EF4444/FFFFFF?text=Ekstrakurikuler+Basket",
      category: "Ekstrakurikuler",
      featured: false,
      date: "2024-11-25",
      uploadedBy: "Pelatih Basket"
    }
  ]);

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedImage, setSelectedImage] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    url: '',
    category: '',
    featured: false,
    uploadedBy: ''
  });
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewUrl, setPreviewUrl] = useState('');
  const [errors, setErrors] = useState({});
  const [selectedCategory, setSelectedCategory] = useState('all');

  const categories = ['Kegiatan Rutin', 'Kegiatan Khusus', 'Prestasi', 'Fasilitas', 'Ekstrakurikuler'];

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      url: '',
      category: '',
      featured: false,
      uploadedBy: ''
    });
    setSelectedFile(null);
    setPreviewUrl('');
    setErrors({});
  };

  const openModal = (mode, image = null) => {
    setModalMode(mode);
    setSelectedImage(image);

    if (mode === 'edit' && image) {
      setFormData({
        title: image.title,
        description: image.description,
        url: image.url,
        category: image.category,
        featured: image.featured,
        uploadedBy: image.uploadedBy
      });
      setPreviewUrl(image.url); // Set preview untuk edit
      setSelectedFile(null);
    } else if (mode === 'create') {
      resetForm();
    }

    setIsModalOpen(true);
  };

  const closeModal = () => {
    setIsModalOpen(false);
    setSelectedImage(null);
    resetForm();
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validasi tipe file
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          file: 'Tipe file tidak didukung. Gunakan JPG, PNG, GIF, atau WebP'
        }));
        return;
      }

      // Validasi ukuran file (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSize) {
        setErrors(prev => ({
          ...prev,
          file: 'Ukuran file terlalu besar. Maksimal 5MB'
        }));
        return;
      }

      setSelectedFile(file);

      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target.result);
      };
      reader.readAsDataURL(file);

      // Clear file error
      if (errors.file) {
        setErrors(prev => ({
          ...prev,
          file: ''
        }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};

    if (!formData.title.trim()) newErrors.title = 'Judul harus diisi';
    if (!formData.description.trim()) newErrors.description = 'Deskripsi harus diisi';
    if (!formData.category) newErrors.category = 'Kategori harus dipilih';
    if (!formData.uploadedBy.trim()) newErrors.uploadedBy = 'Nama uploader harus diisi';

    // Validasi file untuk create, atau jika tidak ada preview URL untuk edit
    if (modalMode === 'create') {
      if (!selectedFile) newErrors.file = 'File gambar harus dipilih';
    } else if (modalMode === 'edit') {
      // Untuk edit, file opsional jika sudah ada gambar sebelumnya
      if (!selectedFile && !previewUrl) newErrors.file = 'File gambar harus dipilih';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) return;

    // Simulasi upload file - dalam implementasi nyata, upload ke server/cloud storage
    let imageUrl = formData.url; // Keep existing URL for edit if no new file

    if (selectedFile) {
      // Simulasi upload file - buat URL dari file yang dipilih
      imageUrl = previewUrl; // Gunakan preview URL sebagai simulasi

      // Dalam implementasi nyata, Anda akan upload file ke server:
      // const formDataUpload = new FormData();
      // formDataUpload.append('image', selectedFile);
      // const response = await fetch('/api/upload', {
      //   method: 'POST',
      //   body: formDataUpload
      // });
      // const result = await response.json();
      // imageUrl = result.url;
    }

    if (modalMode === 'create') {
      const newImage = {
        id: Date.now(),
        ...formData,
        url: imageUrl,
        date: new Date().toISOString().split('T')[0]
      };
      setImages(prev => [newImage, ...prev]);
    } else if (modalMode === 'edit') {
      setImages(prev => prev.map(item =>
        item.id === selectedImage.id
          ? { ...item, ...formData, url: imageUrl }
          : item
      ));
    }

    closeModal();
  };

  const handleDelete = () => {
    setImages(prev => prev.filter(item => item.id !== selectedImage.id));
    closeModal();
  };

  const filteredImages = selectedCategory === 'all' 
    ? images 
    : images.filter(img => img.category === selectedCategory);

  return (
    <AdminLayout>
      <div className="p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">Kelola Galeri</h1>
            <p className="text-gray-600 mt-2">Kelola semua gambar dan foto kegiatan sekolah</p>
          </div>
          <button
            onClick={() => openModal('create')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition duration-300 flex items-center"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Upload Gambar
          </button>
        </div>

        {/* Filter */}
        <div className="mb-6">
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => setSelectedCategory('all')}
              className={`px-4 py-2 rounded-lg font-semibold transition duration-300 ${
                selectedCategory === 'all'
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
            >
              Semua ({images.length})
            </button>
            {categories.map(category => {
              const count = images.filter(img => img.category === category).length;
              return (
                <button
                  key={category}
                  onClick={() => setSelectedCategory(category)}
                  className={`px-4 py-2 rounded-lg font-semibold transition duration-300 ${
                    selectedCategory === category
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  {category} ({count})
                </button>
              );
            })}
          </div>
        </div>

        {/* Gallery Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {filteredImages.map((image) => (
            <div key={image.id} className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition duration-300">
              <div className="relative">
                <img
                  src={image.url}
                  alt={image.title}
                  className="w-full h-48 object-cover"
                />
                {image.featured && (
                  <div className="absolute top-2 left-2">
                    <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
                      Featured
                    </span>
                  </div>
                )}
                <div className="absolute top-2 right-2">
                  <span className="bg-blue-600 text-white px-2 py-1 rounded-full text-xs font-semibold">
                    {image.category}
                  </span>
                </div>
              </div>
              
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-2 line-clamp-2">{image.title}</h3>
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">{image.description}</p>
                
                <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
                  <span>Oleh: {image.uploadedBy}</span>
                  <span>{new Date(image.date).toLocaleDateString('id-ID')}</span>
                </div>
                
                <div className="flex justify-end space-x-2">
                  <button
                    onClick={() => openModal('view', image)}
                    className="text-blue-600 hover:text-blue-900 p-1"
                    title="Lihat"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => openModal('edit', image)}
                    className="text-green-600 hover:text-green-900 p-1"
                    title="Edit"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    onClick={() => openModal('delete', image)}
                    className="text-red-600 hover:text-red-900 p-1"
                    title="Hapus"
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredImages.length === 0 && (
          <div className="text-center py-12">
            <svg className="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <h3 className="text-xl font-semibold text-gray-900 mb-2">Tidak ada gambar</h3>
            <p className="text-gray-600">Belum ada gambar dalam kategori ini</p>
          </div>
        )}

        {/* Create/Edit Modal */}
        <Modal
          isOpen={isModalOpen && (modalMode === 'create' || modalMode === 'edit')}
          onClose={closeModal}
          title={modalMode === 'create' ? 'Upload Gambar Baru' : 'Edit Gambar'}
          size="lg"
        >
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Judul *
              </label>
              <input
                type="text"
                name="title"
                value={formData.title}
                onChange={handleInputChange}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.title ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Masukkan judul gambar"
              />
              {errors.title && <p className="text-red-600 text-sm mt-1">{errors.title}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Upload Gambar *
              </label>
              <div className="space-y-4">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.file ? 'border-red-500' : 'border-gray-300'
                  }`}
                />
                {errors.file && <p className="text-red-600 text-sm mt-1">{errors.file}</p>}

                {/* Preview */}
                {previewUrl && (
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">Preview:</label>
                    <div className="relative inline-block">
                      <img
                        src={previewUrl}
                        alt="Preview"
                        className="max-w-full h-32 object-cover rounded-lg border"
                      />
                      <button
                        type="button"
                        onClick={() => {
                          setPreviewUrl('');
                          setSelectedFile(null);
                          if (modalMode === 'edit') {
                            setPreviewUrl(selectedImage?.url || '');
                          }
                        }}
                        className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                      >
                        ×
                      </button>
                    </div>
                  </div>
                )}

                <p className="text-sm text-gray-500">
                  Format yang didukung: JPG, PNG, GIF, WebP. Maksimal 5MB.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kategori *
                </label>
                <select
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.category ? 'border-red-500' : 'border-gray-300'
                  }`}
                >
                  <option value="">Pilih Kategori</option>
                  {categories.map(cat => (
                    <option key={cat} value={cat}>{cat}</option>
                  ))}
                </select>
                {errors.category && <p className="text-red-600 text-sm mt-1">{errors.category}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Uploaded By *
                </label>
                <input
                  type="text"
                  name="uploadedBy"
                  value={formData.uploadedBy}
                  onChange={handleInputChange}
                  className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                    errors.uploadedBy ? 'border-red-500' : 'border-gray-300'
                  }`}
                  placeholder="Nama uploader"
                />
                {errors.uploadedBy && <p className="text-red-600 text-sm mt-1">{errors.uploadedBy}</p>}
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deskripsi *
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={4}
                className={`w-full px-4 py-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                  errors.description ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder="Deskripsi gambar"
              />
              {errors.description && <p className="text-red-600 text-sm mt-1">{errors.description}</p>}
            </div>

            <div className="flex items-center">
              <input
                type="checkbox"
                id="featured"
                name="featured"
                checked={formData.featured}
                onChange={handleInputChange}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="featured" className="ml-2 block text-sm text-gray-900">
                Jadikan gambar featured
              </label>
            </div>

            <div className="flex justify-end space-x-4 pt-4">
              <button
                type="button"
                onClick={closeModal}
                className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-300"
              >
                Batal
              </button>
              <button
                type="submit"
                className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-300"
              >
                {modalMode === 'create' ? 'Upload' : 'Update'}
              </button>
            </div>
          </form>
        </Modal>

        {/* View Modal */}
        <Modal
          isOpen={isModalOpen && modalMode === 'view'}
          onClose={closeModal}
          title="Detail Gambar"
          size="lg"
        >
          {selectedImage && (
            <div className="space-y-4">
              <div className="text-center">
                <img
                  src={selectedImage.url}
                  alt={selectedImage.title}
                  className="max-w-full h-auto rounded-lg mx-auto"
                  style={{ maxHeight: '400px' }}
                />
              </div>
              
              <div>
                <h3 className="text-lg font-semibold text-gray-900 mb-2">{selectedImage.title}</h3>
                <div className="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                  <span>Kategori: {selectedImage.category}</span>
                  <span>Upload: {selectedImage.uploadedBy}</span>
                  <span>Tanggal: {new Date(selectedImage.date).toLocaleDateString('id-ID')}</span>
                  {selectedImage.featured && (
                    <span className="bg-yellow-500 text-white px-2 py-1 rounded-full text-xs">Featured</span>
                  )}
                </div>
              </div>
              
              <div>
                <h4 className="font-medium text-gray-900 mb-2">Deskripsi:</h4>
                <p className="text-gray-700">{selectedImage.description}</p>
              </div>
              
              <div className="flex justify-end pt-4">
                <button
                  onClick={closeModal}
                  className="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition duration-300"
                >
                  Tutup
                </button>
              </div>
            </div>
          )}
        </Modal>

        {/* Delete Modal */}
        <Modal
          isOpen={isModalOpen && modalMode === 'delete'}
          onClose={closeModal}
          title="Hapus Gambar"
          size="sm"
        >
          {selectedImage && (
            <div>
              <div className="mb-4">
                <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                  <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <p className="text-sm text-gray-600 text-center">
                  Apakah Anda yakin ingin menghapus gambar ini? Tindakan ini tidak dapat dibatalkan.
                </p>
                <p className="text-sm font-medium text-gray-900 text-center mt-2">
                  "{selectedImage.title}"
                </p>
              </div>
              
              <div className="flex justify-end space-x-4">
                <button
                  onClick={closeModal}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition duration-300"
                >
                  Batal
                </button>
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-300"
                >
                  Hapus
                </button>
              </div>
            </div>
          )}
        </Modal>
      </div>
    </AdminLayout>
  );
};

export default GalleryManagement;
