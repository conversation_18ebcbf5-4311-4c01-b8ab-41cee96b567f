import mongoose from 'mongoose';

const gallerySchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [200, 'Title cannot exceed 200 characters']
  },
  description: {
    type: String,
    trim: true,
    maxlength: [500, 'Description cannot exceed 500 characters']
  },
  imageUrl: {
    type: String,
    required: [true, 'Image URL is required']
  },
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: ['Fasilitas', 'Kegiatan', 'Prestasi', 'Ekstrakurikuler', 'Umum'],
    default: 'Umum'
  },
  tags: [{
    type: String,
    trim: true
  }],
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  featured: {
    type: Boolean,
    default: false
  },
  sortOrder: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Index for search and filtering
gallerySchema.index({ category: 1, createdAt: -1 });
gallerySchema.index({ title: 'text', description: 'text' });
gallerySchema.index({ isActive: 1, featured: -1, sortOrder: 1 });

const Gallery = mongoose.model('Gallery', gallerySchema);

export default Gallery;
