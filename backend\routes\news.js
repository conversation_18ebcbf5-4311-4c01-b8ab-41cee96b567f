import express from 'express';
import News from '../models/News.js';
import { authenticateToken } from './auth.js';

const router = express.Router();

// Get all published news (public)
router.get('/', async (req, res) => {
  try {
    const { page = 1, limit = 10, search, featured } = req.query;

    const query = { published: true };

    if (search) {
      query.$text = { $search: search };
    }

    if (featured === 'true') {
      query.featured = true;
    }

    const publishedNews = await News.find(query)
      .populate('author', 'name')
      .sort({ publishedAt: -1, createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await News.countDocuments(query);

    res.json({
      success: true,
      data: publishedNews,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Get news error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get single news by ID (public)
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const newsItem = await News.findOne({
      _id: id,
      published: true
    }).populate('author', 'name');

    if (!newsItem) {
      return res.status(404).json({
        error: 'News not found'
      });
    }

    // Increment views
    newsItem.views += 1;
    await newsItem.save();

    res.json({
      success: true,
      data: newsItem
    });
  } catch (error) {
    console.error('Get news by ID error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get all news for admin (including unpublished)
router.get('/admin/all', authenticateToken, async (req, res) => {
  try {
    const { page = 1, limit = 10, search, status } = req.query;

    const query = {};

    if (search) {
      query.$text = { $search: search };
    }

    if (status === 'published') {
      query.published = true;
    } else if (status === 'draft') {
      query.published = false;
    }

    const allNews = await News.find(query)
      .populate('author', 'name')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await News.countDocuments(query);

    res.json({
      success: true,
      data: allNews,
      total,
      page: parseInt(page),
      pages: Math.ceil(total / limit)
    });
  } catch (error) {
    console.error('Get admin news error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Create new news (admin only)
router.post('/', authenticateToken, async (req, res) => {
  try {
    const { title, content, excerpt, imageUrl, published = false, tags, featured = false } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        error: 'Title and content are required'
      });
    }

    const newNews = new News({
      title,
      content,
      excerpt,
      imageUrl,
      author: req.user.userId,
      published: Boolean(published),
      tags: tags || [],
      featured: Boolean(featured)
    });

    await newNews.save();
    await newNews.populate('author', 'name');

    res.status(201).json({
      success: true,
      message: 'News created successfully',
      data: newNews
    });
  } catch (error) {
    console.error('Create news error:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
});

// Update news (admin only)
router.put('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, excerpt, imageUrl, published, tags, featured } = req.body;

    const newsItem = await News.findById(id);
    if (!newsItem) {
      return res.status(404).json({
        error: 'News not found'
      });
    }

    // Update fields
    if (title) newsItem.title = title;
    if (content) newsItem.content = content;
    if (excerpt !== undefined) newsItem.excerpt = excerpt;
    if (imageUrl !== undefined) newsItem.imageUrl = imageUrl;
    if (published !== undefined) newsItem.published = Boolean(published);
    if (tags) newsItem.tags = tags;
    if (featured !== undefined) newsItem.featured = Boolean(featured);

    await newsItem.save();
    await newsItem.populate('author', 'name');

    res.json({
      success: true,
      message: 'News updated successfully',
      data: newsItem
    });
  } catch (error) {
    console.error('Update news error:', error);
    res.status(500).json({
      error: error.message || 'Internal server error'
    });
  }
});

// Delete news (admin only)
router.delete('/:id', authenticateToken, async (req, res) => {
  try {
    const { id } = req.params;

    const newsItem = await News.findById(id);
    if (!newsItem) {
      return res.status(404).json({
        error: 'News not found'
      });
    }

    await News.findByIdAndDelete(id);

    res.json({
      success: true,
      message: 'News deleted successfully'
    });
  } catch (error) {
    console.error('Delete news error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
