import express from 'express';
import { news } from '../models/data.js';
import { authenticateToken } from './auth.js';

const router = express.Router();

// Get all published news (public)
router.get('/', (req, res) => {
  try {
    const publishedNews = news
      .filter(item => item.published)
      .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json({
      success: true,
      data: publishedNews,
      total: publishedNews.length
    });
  } catch (error) {
    console.error('Get news error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get single news by ID (public)
router.get('/:id', (req, res) => {
  try {
    const { id } = req.params;
    const newsItem = news.find(item => item.id === parseInt(id) && item.published);

    if (!newsItem) {
      return res.status(404).json({
        error: 'News not found'
      });
    }

    res.json({
      success: true,
      data: newsItem
    });
  } catch (error) {
    console.error('Get news by ID error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get all news for admin (including unpublished)
router.get('/admin/all', authenticateToken, (req, res) => {
  try {
    const allNews = news.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

    res.json({
      success: true,
      data: allNews,
      total: allNews.length
    });
  } catch (error) {
    console.error('Get admin news error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Create new news (admin only)
router.post('/', authenticateToken, (req, res) => {
  try {
    const { title, content, excerpt, imageUrl, published = false } = req.body;

    if (!title || !content) {
      return res.status(400).json({
        error: 'Title and content are required'
      });
    }

    const newNews = {
      id: Math.max(...news.map(n => n.id), 0) + 1,
      title,
      content,
      excerpt: excerpt || content.substring(0, 150) + '...',
      imageUrl: imageUrl || null,
      author: 'Administrator',
      published: Boolean(published),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    news.push(newNews);

    res.status(201).json({
      success: true,
      message: 'News created successfully',
      data: newNews
    });
  } catch (error) {
    console.error('Create news error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update news (admin only)
router.put('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { title, content, excerpt, imageUrl, published } = req.body;

    const newsIndex = news.findIndex(item => item.id === parseInt(id));
    if (newsIndex === -1) {
      return res.status(404).json({
        error: 'News not found'
      });
    }

    // Update news item
    const updatedNews = {
      ...news[newsIndex],
      ...(title && { title }),
      ...(content && { content }),
      ...(excerpt && { excerpt }),
      ...(imageUrl !== undefined && { imageUrl }),
      ...(published !== undefined && { published: Boolean(published) }),
      updatedAt: new Date().toISOString()
    };

    news[newsIndex] = updatedNews;

    res.json({
      success: true,
      message: 'News updated successfully',
      data: updatedNews
    });
  } catch (error) {
    console.error('Update news error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Delete news (admin only)
router.delete('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const newsIndex = news.findIndex(item => item.id === parseInt(id));

    if (newsIndex === -1) {
      return res.status(404).json({
        error: 'News not found'
      });
    }

    news.splice(newsIndex, 1);

    res.json({
      success: true,
      message: 'News deleted successfully'
    });
  } catch (error) {
    console.error('Delete news error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
