import { useState, useEffect } from 'react';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:5000/api';

export const useNews = (options = {}) => {
  const {
    page = 1,
    limit = 10,
    search = '',
    featured = false,
    autoFetch = true
  } = options;

  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    page: 1,
    pages: 1,
    total: 0
  });

  const fetchNews = async (fetchOptions = {}) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams({
        page: fetchOptions.page || page,
        limit: fetchOptions.limit || limit,
        ...(fetchOptions.search || search ? { search: fetchOptions.search || search } : {}),
        ...(fetchOptions.featured || featured ? { featured: 'true' } : {})
      });

      const response = await fetch(`${API_BASE_URL}/news?${params}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        setNews(result.data);
        setPagination({
          page: result.page,
          pages: result.pages,
          total: result.total
        });
      } else {
        throw new Error('Failed to fetch news');
      }
    } catch (err) {
      console.error('Error fetching news:', err);
      setError(err.message);

      // Fallback to mock data if API fails
      const mockData = [
        {
          id: 1,
          title: "Prestasi Gemilang Siswa SMA Negeri 1 Jakarta di Olimpiade Matematika Nasional",
          excerpt: "Tim olimpiade matematika sekolah berhasil meraih juara 1 tingkat nasional setelah melalui seleksi ketat dari berbagai daerah di Indonesia.",
          content: "Tim olimpiade matematika SMA Negeri 1 Jakarta berhasil meraih prestasi gemilang dengan meraih juara 1 pada Olimpiade Matematika Nasional 2024. Prestasi ini diraih setelah melalui berbagai tahap seleksi yang ketat mulai dari tingkat sekolah, kabupaten, provinsi, hingga nasional. Tim yang terdiri dari 3 siswa terbaik ini telah mempersiapkan diri selama berbulan-bulan dengan bimbingan intensif dari guru pembimbing. Kepala sekolah menyampaikan rasa bangga dan apresiasi tinggi atas pencapaian luar biasa ini.",
          category: "Prestasi",
          author: "Admin Sekolah",
          date: "2024-12-15T10:00:00Z",
          image: null,
          views: 245,
          featured: true
        },
        {
          id: 2,
          title: "Pelaksanaan Ujian Tengah Semester Ganjil Tahun Ajaran 2024/2025",
          excerpt: "Ujian Tengah Semester akan dilaksanakan mulai tanggal 20-25 Januari 2025 dengan protokol kesehatan yang ketat.",
          content: "Sekolah mengumumkan jadwal pelaksanaan Ujian Tengah Semester Ganjil untuk semua tingkat kelas. Ujian akan berlangsung selama 6 hari dengan sistem shift untuk menghindari kepadatan. Semua siswa diwajibkan hadir tepat waktu dan membawa perlengkapan ujian yang diperlukan.",
          category: "Akademik",
          author: "Wakil Kepala Sekolah",
          date: "2024-12-10T08:30:00Z",
          image: null,
          views: 189,
          featured: false
        },
        {
          id: 3,
          title: "Festival Seni dan Budaya Sekolah 2024 Sukses Digelar",
          excerpt: "Acara tahunan Festival Seni dan Budaya berhasil menampilkan berbagai kreativitas siswa dalam bidang seni, musik, dan tari tradisional.",
          content: "Festival Seni dan Budaya SMA Negeri 1 Jakarta tahun 2024 telah sukses digelar dengan antusiasme tinggi dari seluruh siswa. Acara ini menampilkan berbagai pertunjukan mulai dari tari tradisional, musik modern, drama, hingga pameran karya seni rupa siswa.",
          category: "Kegiatan",
          author: "Koordinator Ekstrakurikuler",
          date: "2024-12-05T14:00:00Z",
          image: null,
          views: 312,
          featured: true
        },
        {
          id: 4,
          title: "Program Beasiswa Prestasi untuk Siswa Berprestasi Tahun 2025",
          excerpt: "Sekolah membuka program beasiswa prestasi untuk siswa yang memiliki prestasi akademik dan non-akademik outstanding.",
          content: "Dalam rangka mendukung siswa berprestasi, SMA Negeri 1 Jakarta membuka program beasiswa prestasi untuk tahun ajaran 2025. Program ini ditujukan untuk siswa yang memiliki prestasi akademik maupun non-akademik yang luar biasa.",
          category: "Pengumuman",
          author: "Kepala Sekolah",
          date: "2024-12-01T09:00:00Z",
          image: null,
          views: 156,
          featured: false
        },
        {
          id: 5,
          title: "Kunjungan Industri ke Perusahaan Teknologi Terkemuka",
          excerpt: "Siswa kelas XII IPA berkesempatan mengunjungi perusahaan teknologi untuk melihat langsung penerapan ilmu di dunia kerja.",
          content: "Sebagai bagian dari program pembelajaran kontekstual, siswa kelas XII IPA melakukan kunjungan industri ke beberapa perusahaan teknologi terkemuka di Jakarta. Kegiatan ini bertujuan memberikan gambaran nyata tentang penerapan ilmu pengetahuan di dunia kerja.",
          category: "Kegiatan",
          author: "Guru Pembimbing",
          date: "2024-11-28T11:30:00Z",
          image: null,
          views: 203,
          featured: false
        },
        {
          id: 6,
          title: "Peningkatan Fasilitas Laboratorium Komputer dengan Teknologi Terbaru",
          excerpt: "Sekolah melakukan upgrade fasilitas laboratorium komputer dengan perangkat terbaru untuk mendukung pembelajaran digital.",
          content: "Dalam upaya meningkatkan kualitas pembelajaran, SMA Negeri 1 Jakarta telah melakukan upgrade fasilitas laboratorium komputer dengan perangkat terbaru. Investasi ini diharapkan dapat mendukung pembelajaran digital yang semakin penting di era modern.",
          category: "Fasilitas",
          author: "Kepala Lab Komputer",
          date: "2024-11-25T13:15:00Z",
          image: null,
          views: 178,
          featured: false
        }
      ];

      setNews(mockData);
      setPagination({
        page: 1,
        pages: 1,
        total: mockData.length
      });
    } finally {
      setLoading(false);
    }
  };

  const getNewsById = async (id) => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`${API_BASE_URL}/news/${id}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result = await response.json();

      if (result.success) {
        return result.data;
      } else {
        throw new Error('News not found');
      }
    } catch (err) {
      console.error('Error fetching news by ID:', err);
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (autoFetch) {
      fetchNews();
    }
  }, [page, limit, search, featured, autoFetch]); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    news,
    loading,
    error,
    pagination,
    fetchNews,
    getNewsById,
    refetch: () => fetchNews()
  };
};

export const useSingleNews = (id) => {
  const [newsItem, setNewsItem] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchNewsItem = async () => {
      if (!id) return;

      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`${API_BASE_URL}/news/${id}`);

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();

        if (result.success) {
          setNewsItem(result.data);
        } else {
          throw new Error('News not found');
        }
      } catch (err) {
        console.error('Error fetching news item:', err);
        setError(err.message);
        setNewsItem(null);
      } finally {
        setLoading(false);
      }
    };

    fetchNewsItem();
  }, [id]);

  return {
    newsItem,
    loading,
    error
  };
};

export default useNews;
