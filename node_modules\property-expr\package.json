{"name": "property-expr", "version": "2.0.6", "description": "tiny util for getting and setting deep object props safely", "main": "index.js", "types": "index.d.ts", "files": ["index.js", "index.d.ts", "compiler.js"], "scripts": {"test": "node ./test.js", "debug": "node --inspect-brk ./test.js"}, "repository": {"type": "git", "url": "https://github.com/jquense/expr/"}, "keywords": ["expr", "expression", "setter", "getter", "deep", "property", "<PERSON><PERSON><PERSON><PERSON>", "accessor"], "author": "@monasticpanic Jason <PERSON>", "license": "MIT", "prettier": {"singleQuote": true, "semi": false}}