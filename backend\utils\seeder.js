import User from '../models/User.js';
import News from '../models/News.js';
import Gallery from '../models/Gallery.js';
import Settings from '../models/Settings.js';

const seedDatabase = async () => {
  try {
    console.log('🌱 Starting database seeding...');

    // Clear existing data
    await User.deleteMany({});
    await News.deleteMany({});
    await Gallery.deleteMany({});
    await Settings.deleteMany({});

    // Create admin user
    const adminUser = new User({
      name: 'Administrator',
      email: '<EMAIL>',
      password: 'admin123',
      role: 'admin'
    });
    await adminUser.save();
    console.log('✅ Admin user created');

    // Create sample news
    const sampleNews = [
      {
        title: 'Penerimaan Siswa Baru Tahun Ajaran 2025/2026',
        content: 'SMA Negeri 1 Jakarta membuka pendaftaran siswa baru untuk tahun ajaran 2025/2026. Pendaftaran dibuka mulai tanggal 1 Februari hingga 28 Februari 2025. Calon siswa dapat mendaftar secara online melalui website resmi sekolah.',
        excerpt: 'Pendaftaran siswa baru dibuka mulai 1 Februari 2025',
        imageUrl: '/uploads/news/psb-2025.jpg',
        author: adminUser._id,
        published: true,
        featured: true,
        tags: ['pendaftaran', 'siswa baru', 'PSB']
      },
      {
        title: 'Prestasi Siswa dalam Olimpiade Matematika Nasional',
        content: 'Siswa SMA Negeri 1 Jakarta berhasil meraih medali emas dalam Olimpiade Matematika Nasional 2024. Prestasi ini membanggakan dan menunjukkan kualitas pendidikan di sekolah kami.',
        excerpt: 'Siswa meraih medali emas Olimpiade Matematika Nasional',
        imageUrl: '/uploads/news/olimpiade-math.jpg',
        author: adminUser._id,
        published: true,
        tags: ['prestasi', 'olimpiade', 'matematika']
      },
      {
        title: 'Kegiatan Bakti Sosial di Panti Asuhan',
        content: 'Siswa-siswi SMA Negeri 1 Jakarta mengadakan kegiatan bakti sosial di Panti Asuhan Kasih Sayang. Kegiatan ini bertujuan untuk menumbuhkan rasa empati dan kepedulian sosial.',
        excerpt: 'Kegiatan bakti sosial untuk menumbuhkan empati siswa',
        imageUrl: '/uploads/news/baksos.jpg',
        author: adminUser._id,
        published: true,
        tags: ['bakti sosial', 'empati', 'kegiatan']
      }
    ];

    await News.insertMany(sampleNews);
    console.log('✅ Sample news created');

    // Create sample gallery
    const sampleGallery = [
      {
        title: 'Gedung Utama Sekolah',
        description: 'Gedung utama SMA Negeri 1 Jakarta yang megah dan modern',
        imageUrl: '/uploads/gallery/gedung-utama.jpg',
        category: 'Fasilitas',
        uploadedBy: adminUser._id,
        featured: true
      },
      {
        title: 'Laboratorium Komputer',
        description: 'Laboratorium komputer dengan fasilitas terkini untuk mendukung pembelajaran teknologi',
        imageUrl: '/uploads/gallery/lab-komputer.jpg',
        category: 'Fasilitas',
        uploadedBy: adminUser._id
      },
      {
        title: 'Kegiatan Ekstrakurikuler Pramuka',
        description: 'Siswa-siswi mengikuti kegiatan pramuka dengan antusias',
        imageUrl: '/uploads/gallery/pramuka.jpg',
        category: 'Kegiatan',
        uploadedBy: adminUser._id
      },
      {
        title: 'Perpustakaan Sekolah',
        description: 'Perpustakaan dengan koleksi buku yang lengkap dan suasana yang nyaman',
        imageUrl: '/uploads/gallery/perpustakaan.jpg',
        category: 'Fasilitas',
        uploadedBy: adminUser._id
      }
    ];

    await Gallery.insertMany(sampleGallery);
    console.log('✅ Sample gallery created');

    // Create school settings
    const schoolSettings = [
      { key: 'schoolName', value: 'SMA Negeri 1 Jakarta', type: 'string', category: 'general', updatedBy: adminUser._id },
      { key: 'schoolShortName', value: 'SMAN 1 Jakarta', type: 'string', category: 'general', updatedBy: adminUser._id },
      { key: 'schoolEmail', value: '<EMAIL>', type: 'string', category: 'contact', updatedBy: adminUser._id },
      { key: 'schoolPhone', value: '021-12345678', type: 'string', category: 'contact', updatedBy: adminUser._id },
      { key: 'schoolAddress', value: 'Jl. Pendidikan No. 123, Menteng, Jakarta Pusat, DKI Jakarta 10310', type: 'string', category: 'contact', updatedBy: adminUser._id },
      { key: 'schoolWebsite', value: 'www.sman1jakarta.sch.id', type: 'string', category: 'contact', updatedBy: adminUser._id },
      { key: 'schoolDescription', value: 'SMA Negeri 1 Jakarta adalah sekolah menengah atas negeri yang berkomitmen untuk memberikan pendidikan berkualitas tinggi dengan mengembangkan potensi akademik dan karakter siswa.', type: 'string', category: 'general', updatedBy: adminUser._id },
      { key: 'schoolMotto', value: 'Unggul dalam Prestasi, Berkarakter, dan Berwawasan Global', type: 'string', category: 'general', updatedBy: adminUser._id },
      { key: 'logoUrl', value: '/uploads/logo/school-logo.png', type: 'string', category: 'appearance', updatedBy: adminUser._id }
    ];

    await Settings.insertMany(schoolSettings);
    console.log('✅ School settings created');

    console.log('🎉 Database seeding completed successfully!');
    console.log('📧 Admin login: <EMAIL>');
    console.log('🔑 Admin password: admin123');

  } catch (error) {
    console.error('❌ Database seeding failed:', error);
    throw error;
  }
};

export default seedDatabase;
