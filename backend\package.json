{"name": "backend", "version": "1.0.0", "main": "server.js", "type": "module", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node scripts/seed.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["express", "nodejs", "api", "school", "crud"], "author": "", "license": "ISC", "description": "Backend API for School Management System", "dependencies": {"bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.18.2", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.16.4", "morgan": "^1.10.1", "multer": "^2.0.2"}, "devDependencies": {"concurrently": "^9.2.0", "nodemon": "^3.1.10"}}