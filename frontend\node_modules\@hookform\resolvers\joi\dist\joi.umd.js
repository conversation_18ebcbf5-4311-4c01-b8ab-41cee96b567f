!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@hookform/resolvers"),require("react-hook-form")):"function"==typeof define&&define.amd?define(["exports","@hookform/resolvers","react-hook-form"],r):r((e||self).hookformResolversJoi={},e.hookformResolvers,e.ReactHookForm)}(this,function(e,r,o){e.joiResolver=function(e,t,n){return void 0===t&&(t={abortEarly:!1}),void 0===n&&(n={}),function(i,s,a){try{var u=function(){return f.error?{values:{},errors:r.toNestErrors((e=f.error,t=!a.shouldUseNativeValidation&&"all"===a.criteriaMode,e.details.length?e.details.reduce(function(e,r){var n=r.path.join(".");if(e[n]||(e[n]={message:r.message,type:r.type}),t){var i=e[n].types,s=i&&i[r.type];e[n]=o.appendErrors(n,t,e,r.type,s?[].concat(s,r.message):r.message)}return e},{}):{}),a)}:(a.shouldUseNativeValidation&&r.validateFieldsNatively({},a),{errors:{},values:f.value});var e,t},l=Object.assign({},t,{context:s}),f={},c=function(){if("sync"===n.mode)f=e.validate(i,l);else{var r=function(e,r){try{var o=e()}catch(e){return r(e)}return o&&o.then?o.then(void 0,r):o}(function(){return Promise.resolve(e.validateAsync(i,l)).then(function(e){f.value=e})},function(e){f.error=e});if(r&&r.then)return r.then(function(){})}}();return Promise.resolve(c&&c.then?c.then(u):u())}catch(e){return Promise.reject(e)}}}});
//# sourceMappingURL=joi.umd.js.map
