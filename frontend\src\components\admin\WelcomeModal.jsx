import { AnimatePresence } from 'framer-motion';
import { useEffect } from 'react';

const WelcomeModal = ({ isOpen, onClose, userRole = 'Admin', userName = 'Administrator' }) => {
  // Auto close modal after 3 seconds
  useEffect(() => {
    if (isOpen) {
      const timer = setTimeout(() => {
        onClose();
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const getRoleDisplayName = (role) => {
    switch (role.toLowerCase()) {
      case 'admin':
        return 'Administrator';
      case 'superadmin':
        return 'Super Administrator';
      case 'moderator':
        return 'Moderator';
      default:
        return 'Administrator';
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
            onClick={onClose}
          />
          
          {/* Modal */}
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all animate-bounce"
               style={{ animationDuration: '0.5s', animationIterationCount: '1' }}>
            <div className="p-6">
              {/* Success Icon */}
              <div className="flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-green-100 rounded-full">
                <svg 
                  className="w-8 h-8 text-green-600" 
                  fill="none" 
                  stroke="currentColor" 
                  viewBox="0 0 24 24"
                >
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </svg>
              </div>

              {/* Title */}
              <h3 className="text-xl font-bold text-gray-900 text-center mb-2">
                Selamat Datang!
              </h3>

              {/* Welcome Message */}
              <div className="text-center mb-4">
                <p className="text-sm text-gray-600 mb-1">
                  Login berhasil sebagai
                </p>
                <p className="text-lg font-semibold text-blue-600">
                  {getRoleDisplayName(userRole)}
                </p>
                {userName && userName !== 'Administrator' && (
                  <p className="text-sm text-gray-500 mt-1">
                    Halo, {userName}
                  </p>
                )}
              </div>

              {/* Success Message */}
              <p className="text-sm text-gray-600 text-center mb-6">
                Anda berhasil masuk ke sistem administrasi. Selamat bekerja!
              </p>

              {/* Close Button */}
              <div className="flex justify-center">
                <button
                  onClick={onClose}
                  className="px-6 py-2 text-sm font-medium text-white bg-green-600 border border-transparent rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
                >
                  Mulai Bekerja
                </button>
              </div>

              {/* Auto close indicator */}
              <div className="mt-4 text-center">
                <p className="text-xs text-gray-400">
                  Modal akan tertutup otomatis dalam 3 detik
                </p>
              </div>
            </div>
          </div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default WelcomeModal;
