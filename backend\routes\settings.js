import express from 'express';
import Settings from '../models/Settings.js';
import { authenticateToken } from './auth.js';

const router = express.Router();

// Get school settings (public)
router.get('/', async (req, res) => {
  try {
    const settings = await Settings.getPublicSettings();

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get all settings (admin only)
router.get('/admin/all', authenticateToken, async (req, res) => {
  try {
    const settings = await Settings.find({})
      .populate('updatedBy', 'name')
      .sort({ category: 1, key: 1 });

    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Get admin settings error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update school settings (admin only)
router.put('/', authenticateToken, async (req, res) => {
  try {
    const updates = req.body;
    const updatedSettings = [];

    for (const [key, value] of Object.entries(updates)) {
      if (value !== undefined) {
        const setting = await Settings.setSetting(key, value, req.user.userId);
        updatedSettings.push(setting);
      }
    }

    const allSettings = await Settings.getPublicSettings();

    res.json({
      success: true,
      message: 'Settings updated successfully',
      data: allSettings,
      updated: updatedSettings
    });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
