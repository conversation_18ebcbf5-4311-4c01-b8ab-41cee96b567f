import express from 'express';
import { settings } from '../models/data.js';
import { authenticateToken } from './auth.js';

const router = express.Router();

// Get school settings (public)
router.get('/', (req, res) => {
  try {
    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Get settings error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update school settings (admin only)
router.put('/', authenticateToken, (req, res) => {
  try {
    const {
      schoolName,
      schoolShortName,
      schoolEmail,
      schoolPhone,
      schoolAddress,
      schoolWebsite,
      schoolDescription,
      schoolMotto,
      logoUrl
    } = req.body;

    // Update settings
    if (schoolName) settings.schoolName = schoolName;
    if (schoolShortName) settings.schoolShortName = schoolShortName;
    if (schoolEmail) settings.schoolEmail = schoolEmail;
    if (schoolPhone) settings.schoolPhone = schoolPhone;
    if (schoolAddress) settings.schoolAddress = schoolAddress;
    if (schoolWebsite) settings.schoolWebsite = schoolWebsite;
    if (schoolDescription) settings.schoolDescription = schoolDescription;
    if (schoolMotto) settings.schoolMotto = schoolMotto;
    if (logoUrl !== undefined) settings.logoUrl = logoUrl;

    settings.updatedAt = new Date().toISOString();

    res.json({
      success: true,
      message: 'Settings updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
