import express from 'express';
import { contacts } from '../models/data.js';
import { authenticateToken } from './auth.js';

const router = express.Router();

// Submit contact form (public)
router.post('/', (req, res) => {
  try {
    const { name, email, subject, message } = req.body;

    if (!name || !email || !subject || !message) {
      return res.status(400).json({
        error: 'All fields are required'
      });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        error: 'Invalid email format'
      });
    }

    const newContact = {
      id: Math.max(...contacts.map(c => c.id), 0) + 1,
      name,
      email,
      subject,
      message,
      status: 'unread',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    contacts.push(newContact);

    res.status(201).json({
      success: true,
      message: 'Message sent successfully',
      data: {
        id: newContact.id,
        name: newContact.name,
        email: newContact.email,
        subject: newContact.subject,
        createdAt: newContact.createdAt
      }
    });
  } catch (error) {
    console.error('Submit contact error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get all contact messages (admin only)
router.get('/', authenticateToken, (req, res) => {
  try {
    const { status } = req.query;
    
    let filteredContacts = contacts;
    if (status) {
      filteredContacts = contacts.filter(contact => contact.status === status);
    }

    const sortedContacts = filteredContacts.sort((a, b) => 
      new Date(b.createdAt) - new Date(a.createdAt)
    );

    res.json({
      success: true,
      data: sortedContacts,
      total: sortedContacts.length
    });
  } catch (error) {
    console.error('Get contacts error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Get single contact message (admin only)
router.get('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const contact = contacts.find(item => item.id === parseInt(id));

    if (!contact) {
      return res.status(404).json({
        error: 'Contact message not found'
      });
    }

    res.json({
      success: true,
      data: contact
    });
  } catch (error) {
    console.error('Get contact by ID error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Update contact status (admin only)
router.put('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const { status } = req.body;

    if (!status || !['read', 'unread'].includes(status)) {
      return res.status(400).json({
        error: 'Valid status is required (read or unread)'
      });
    }

    const contactIndex = contacts.findIndex(item => item.id === parseInt(id));
    if (contactIndex === -1) {
      return res.status(404).json({
        error: 'Contact message not found'
      });
    }

    contacts[contactIndex].status = status;
    contacts[contactIndex].updatedAt = new Date().toISOString();

    res.json({
      success: true,
      message: 'Contact status updated successfully',
      data: contacts[contactIndex]
    });
  } catch (error) {
    console.error('Update contact error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

// Delete contact message (admin only)
router.delete('/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    const contactIndex = contacts.findIndex(item => item.id === parseInt(id));

    if (contactIndex === -1) {
      return res.status(404).json({
        error: 'Contact message not found'
      });
    }

    contacts.splice(contactIndex, 1);

    res.json({
      success: true,
      message: 'Contact message deleted successfully'
    });
  } catch (error) {
    console.error('Delete contact error:', error);
    res.status(500).json({
      error: 'Internal server error'
    });
  }
});

export default router;
