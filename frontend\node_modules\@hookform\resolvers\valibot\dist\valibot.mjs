import{toNestErrors as e}from"@hookform/resolvers";import{appendErrors as r}from"react-hook-form";import{safeParseAsync as s,getDotPath as t}from"valibot";var o=function(o,a,i){return void 0===i&&(i={}),function(u,n,m){try{var f=!m.shouldUseNativeValidation&&"all"===m.criteriaMode;return Promise.resolve(s(o,u,Object.assign({},a,{abortPipeEarly:!f}))).then(function(s){if(s.issues){for(var o={};s.issues.length;){var a=s.issues[0],n=t(a);if(n&&(o[n]||(o[n]={message:a.message,type:a.type}),f)){var v=o[n].types,c=v&&v[a.type];o[n]=r(n,f,o,a.type,c?[].concat(c,a.message):a.message)}s.issues.shift()}return{values:{},errors:e(o,m)}}return{values:i.raw?u:s.output,errors:{}}})}catch(e){return Promise.reject(e)}}};export{o as valibotResolver};
//# sourceMappingURL=valibot.module.js.map
