import { observeTimeline } from '../render/dom/scroll/observe.mjs';
import { supportsScrollTimeline } from '../render/dom/scroll/supports.mjs';

class GroupPlaybackControls {
    constructor(animations) {
        this.animations = animations.filter(Boolean);
    }
    then(onResolve, onReject) {
        return Promise.all(this.animations).then(onResolve).catch(onReject);
    }
    /**
     * TODO: Filter out cancelled or stopped animations before returning
     */
    getAll(propName) {
        return this.animations[0][propName];
    }
    setAll(propName, newValue) {
        for (let i = 0; i < this.animations.length; i++) {
            this.animations[i][propName] = newValue;
        }
    }
    attachTimeline(timeline) {
        const cancelAll = this.animations.map((animation) => {
            if (supportsScrollTimeline() && animation.attachTimeline) {
                animation.attachTimeline(timeline);
            }
            else {
                animation.pause();
                return observeTimeline((progress) => {
                    animation.time = animation.duration * progress;
                }, timeline);
            }
        });
        return () => {
            cancelAll.forEach((cancelTimeline, i) => {
                if (cancelTimeline)
                    cancelTimeline();
                this.animations[i].stop();
            });
        };
    }
    get time() {
        return this.getAll("time");
    }
    set time(time) {
        this.setAll("time", time);
    }
    get speed() {
        return this.getAll("speed");
    }
    set speed(speed) {
        this.setAll("speed", speed);
    }
    get duration() {
        let max = 0;
        for (let i = 0; i < this.animations.length; i++) {
            max = Math.max(max, this.animations[i].duration);
        }
        return max;
    }
    runAll(methodName) {
        this.animations.forEach((controls) => controls[methodName]());
    }
    play() {
        this.runAll("play");
    }
    pause() {
        this.runAll("pause");
    }
    stop() {
        this.runAll("stop");
    }
    cancel() {
        this.runAll("cancel");
    }
    complete() {
        this.runAll("complete");
    }
}

export { GroupPlaybackControls };
