[build]
  # Directory to change to before starting a build
  base = "frontend/"
  
  # Directory that contains the deploy-ready HTML files and assets generated by the build
  publish = "frontend/dist/"
  
  # Default build command
  command = "npm run build"

[build.environment]
  # Node.js version
  NODE_VERSION = "18"

# Redirect rules for SPA (Single Page Application)
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and performance
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/static/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Environment variables (you can set these in Netlify dashboard)
# VITE_API_URL = "https://your-api-url.com"
# VITE_APP_NAME = "SMA Negeri 1 Jakarta"
